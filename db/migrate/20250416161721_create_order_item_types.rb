class CreateOrderItemTypes < ActiveRecord::Migration[7.0]
  def change
    create_table :order_item_types do |t|
      t.string :name
      t.string :code
      t.integer :unit
      t.decimal :rate
      t.decimal :tax_rate, default: 18.0
      t.boolean :is_tax_inclusive, default: false
      t.boolean :unpaid_work, default: false
      t.text :description
      t.boolean :is_rate_overridable, default: true
      t.integer :status

      t.timestamps
    end
  end
end
